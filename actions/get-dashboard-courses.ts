import { Prisma } from '@prisma/client'
import { db } from '@/lib/db'
import { getProgress } from './get-progress'

type CourseWithProgressAndCategory = Prisma.CourseGetPayload<{ include: { category: true; chapters: true } }> & {
  progress: number
}

type DashboardCourses = {
  completedCourses: CourseWithProgressAndCategory[]
  coursesInProgress: CourseWithProgressAndCategory[]
}

export async function getDashboardCourses(userId: string): Promise<DashboardCourses> {
  try {
    // Get purchased courses
    const purchasedCourses = await db.purchase.findMany({
      where: { userId },
      select: { course: { include: { category: true, chapters: { where: { isPublished: true } } } } },
    })

    // Get free courses
    const freeCourses = await db.course.findMany({
      where: { isFree: true, isPublished: true },
      include: { category: true, chapters: { where: { isPublished: true } } },
    })

    const purchasedCoursesData = purchasedCourses.map((purchase) => purchase.course)
    const allCourses = [...purchasedCoursesData, ...freeCourses] as CourseWithProgressAndCategory[]

    // Remove duplicates (in case user purchased a free course)
    const uniqueCourses = allCourses.filter((course, index, self) =>
      index === self.findIndex(c => c.id === course.id)
    )

    for (const course of uniqueCourses) {
      const progress = await getProgress(userId, course.id)
      course.progress = progress
    }

    const completedCourses = uniqueCourses.filter((course) => course.progress === 100)
    const coursesInProgress = uniqueCourses.filter((course) => (course?.progress ?? 0) < 100)

    return {
      completedCourses,
      coursesInProgress,
    }
  } catch {
    return {
      completedCourses: [],
      coursesInProgress: [],
    }
  }
}
