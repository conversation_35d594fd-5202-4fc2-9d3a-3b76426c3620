{"name": "next-lms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@clerk/nextjs": "^6.19.3", "@hello-pangea/dnd": "^16.6.0", "@hookform/resolvers": "^3.10.0", "@mux/mux-node": "^7.3.5", "@mux/mux-player-react": "^2.9.1", "@prisma/client": "^5.22.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@tanstack/react-table": "^8.21.3", "@uploadthing/react": "^7.3.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.510.0", "next": "^15.3.2", "query-string": "^8.2.0", "react": "^19.1.0", "react-confetti": "^6.4.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "react-quill-new": "^3.4.6", "recharts": "^2.15.3", "stripe": "^14.25.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.7.2", "zod": "^3.24.4", "zustand": "^4.5.6"}, "devDependencies": {"@types/node": "^20.17.47", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.21", "cz-conventional-changelog": "^3.3.0", "eslint": "^8", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.5.14", "prisma": "^5.22.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}