const { PrismaClient } = require('@prisma/client')

const database = new PrismaClient()

async function main() {
  try {
    await database.category.createMany({
      data: [
        { name: '<PERSON><PERSON> Komputer' },
        { name: '<PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON>' },
        { name: 'Pembuatan Film' },
        { name: '<PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON>' },
        { name: '<PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON>' },
      ],
    })

    console.log('🟢 Seed script run successfully!🟢')
  } catch (error) {
    console.log('🔴 Error in seed script 🔴', error)
  } finally {
    await database.$disconnect()
  }
}

main()
