generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Course {
  id          String  @id @default(cuid())
  createdById String
  title       String
  description String?
  imageUrl    String?
  price       Float?
  isPublished Boolean @default(false)

  attachments Attachment[]
  category    Category?    @relation(fields: [categoryId], references: [id])
  categoryId  String?
  chapters    Chapter[]
  purchases   Purchase[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Category {
  id      String   @id @default(cuid())
  name    String   @unique
  courses Course[]
}

model Attachment {
  id String @id @default(cuid())

  name String
  url  String

  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([courseId])
}

model Chapter {
  id          String  @id @default(cuid())
  title       String
  description String?
  videoUrl    String?
  position    Int
  isPublished Boolean @default(false)
  isFree      Boolean @default(false)

  course       Course         @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId     String
  muxData      MuxData?
  userProgress UserProgress[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model MuxData {
  id         String  @id @default(cuid())
  assetId    String
  playbackId String?

  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)
  chapterId String  @unique
}

model UserProgress {
  id     String @id @default(cuid())
  userId String

  chapter     Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)
  chapterId   String
  isCompleted Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, chapterId])
}

model Purchase {
  id     String @id @default(cuid())
  userId String

  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, courseId])
}

model StripeCustomer {
  id String @id @default(cuid())

  userid           String @unique
  stripeCustomerId String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
