/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['utfs.io', 'oheefmxm47.ufs.sh'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'utfs.io',
      },
      {
        protocol: 'https',
        hostname: 'oheefmxm47.ufs.sh',
      },
      {
        protocol: 'https',
        hostname: '**.ufs.sh',
      },
    ],
  },
}

module.exports = nextConfig
