import { Snap } from 'doku-nodejs-library'

export const doku = new Snap({
  isProduction: process.env.NODE_ENV === 'production',
  clientId: process.env.DOKU_CLIENT_ID!,
  secretKey: process.env.DOKU_SECRET_KEY!,
  privateKey: process.env.DOKU_PRIVATE_KEY!,
})

export interface DokuPaymentRequest {
  orderId: string
  amount: number
  customerName: string
  customerEmail: string
  description: string
  successUrl: string
  cancelUrl: string
}

export interface DokuPaymentResponse {
  paymentUrl: string
  orderId: string
  status: string
}

export async function createDokuPayment(request: DokuPaymentRequest): Promise<DokuPaymentResponse> {
  try {
    const paymentRequest = {
      order: {
        amount: request.amount,
        invoice_number: request.orderId,
        currency: 'IDR',
      },
      payment: {
        payment_due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      },
      customer: {
        name: request.customerName,
        email: request.customerEmail,
      },
      virtual_account_info: {
        info1: request.description,
        info2: 'Course Purchase',
        info3: 'LMS Payment',
      },
    }

    const response = await doku.createVa(paymentRequest)
    
    return {
      paymentUrl: response.virtual_account_info?.virtual_account_number || '',
      orderId: request.orderId,
      status: 'pending',
    }
  } catch (error) {
    console.error('Doku payment creation failed:', error)
    throw new Error('Failed to create Doku payment')
  }
}

export async function checkDokuPaymentStatus(orderId: string) {
  try {
    const response = await doku.checkStatusVa({
      original_partner_reference_no: orderId,
    })
    
    return {
      orderId,
      status: response.transaction_status_desc,
      paidAmount: response.paid_amount,
    }
  } catch (error) {
    console.error('Doku status check failed:', error)
    throw new Error('Failed to check Doku payment status')
  }
}
