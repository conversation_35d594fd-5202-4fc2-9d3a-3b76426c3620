export function isMuxConfigured(): boolean {
  const tokenId = process.env.MUX_TOKEN_ID
  const tokenSecret = process.env.MUX_TOKEN_SECRET
  
  // Check if Mux credentials are properly configured (not placeholders)
  return !!(
    tokenId && 
    tokenSecret && 
    tokenId !== '543ba831-6159-4456-bb27-144f7d4e222f' && // placeholder value
    tokenSecret !== '2kzSI2QQnkXuL2g7+YS8f1xZ7PYVUSYcynGRwvtZbgCYoQEYoiAnMJtY06GSAT8TO54g/+lMT9c' // placeholder value
  )
}

export function getMuxErrorMessage(): string {
  return 'Konfigurasi Mux belum diatur. Silakan hubungi administrator.'
}
