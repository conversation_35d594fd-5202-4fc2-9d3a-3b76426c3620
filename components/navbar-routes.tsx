'use client'

import { UserButton, useAuth } from '@clerk/nextjs'
import { usePathname } from 'next/navigation'
import { LogOut } from 'lucide-react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import { SearchInput } from './search-input'
import { LanguageSwitcher } from './language-switcher'
import { isTeacher } from '@/lib/teacher'

export const NavbarRoutes = () => {
  const { userId } = useAuth()
  const t = useTranslations('navigation')

  const pathname = usePathname()

  const isTeacherPage = pathname?.includes('/teacher')
  const isCoursePage = pathname?.includes('/courses')
  const isSearchPage = pathname?.includes('/search')

  return (
    <>
      {isSearchPage && (
        <div className="hidden md:block">
          <SearchInput />
        </div>
      )}
      <div className="ml-auto flex gap-x-2">
        {isTeacherPage || isCoursePage ? (
          <Link href="/">
            <Button size="sm" variant="ghost">
              <LogOut className="mr-2 h-4 w-4" />
              {t('exit')}
            </Button>
          </Link>
        ) : isTeacher(userId) ? (
          <Link href="/teacher/courses">
            <Button size="sm" variant="ghost">
              {t('teacherMode')}
            </Button>
          </Link>
        ) : null}
        <LanguageSwitcher />
        <UserButton />
      </div>
    </>
  )
}
