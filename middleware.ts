import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import createIntlMiddleware from 'next-intl/middleware'

const publicRoutes = ['/api/webhook']
const isPublicRoute = createRouteMatcher(publicRoutes)

const protectedRoutes = ['/', '/(api|trpc)']
const isProtectedRoute = createRouteMatcher(protectedRoutes)

const intlMiddleware = createIntlMiddleware({
  locales: ['en', 'id'],
  defaultLocale: 'en'
})

export default clerkMiddleware(async (auth, req) => {
  // Handle internationalization first
  const intlResponse = intlMiddleware(req)

  if (!isPublicRoute(req) && isProtectedRoute(req)) {
    await auth.protect()
  }

  return intlResponse
})

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api|trpc)(.*)'],
}
