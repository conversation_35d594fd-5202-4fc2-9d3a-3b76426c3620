import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import createIntlMiddleware from 'next-intl/middleware'

const publicRoutes = ['/api/webhook', '/api/doku-webhook']
const isPublicRoute = createRouteMatcher(publicRoutes)

const protectedRoutes = ['/((?!api|_next|_static|favicon.ico).*)']
const isProtectedRoute = createRouteMatcher(protectedRoutes)

const intlMiddleware = createIntlMiddleware({
  locales: ['en', 'id'],
  defaultLocale: 'en',
  localePrefix: 'always'
})

export default clerkMiddleware(async (auth, req) => {
  // Skip auth for API routes and static files
  if (req.nextUrl.pathname.startsWith('/api') ||
      req.nextUrl.pathname.startsWith('/_next') ||
      req.nextUrl.pathname.startsWith('/_static') ||
      req.nextUrl.pathname === '/favicon.ico') {
    return
  }

  // Handle internationalization first
  const intlResponse = intlMiddleware(req)

  if (!isPublicRoute(req) && isProtectedRoute(req)) {
    await auth.protect()
  }

  return intlResponse
})

export const config = {
  matcher: ['/((?!api|_next|_static|favicon.ico).*)'],
}
