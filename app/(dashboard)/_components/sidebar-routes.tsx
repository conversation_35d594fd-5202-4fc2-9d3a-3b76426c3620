'use client'

import { <PERSON><PERSON><PERSON>, Compass, Layout, List, Tags } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { SidebarItem } from './sidebar-item'

const guestRoutes = [
  {
    icon: Layout,
    label: '<PERSON><PERSON>',
    href: '/',
  },
  {
    icon: Compass,
    label: '<PERSON><PERSON><PERSON><PERSON>',
    href: '/search',
  },
]

const teacherRoutes = [
  {
    icon: List,
    label: 'Kursus',
    href: '/teacher/courses',
  },
  {
    icon: Tags,
    label: 'Kategori',
    href: '/teacher/categories',
  },
  {
    icon: Bar<PERSON>hart,
    label: 'Analitik',
    href: '/teacher/analytics',
  },
]

export const SidebarRoutes = () => {
  const pathname = usePathname()

  const isTeacherPage = pathname?.startsWith('/teacher')

  const routes = isTeacherPage ? teacherRoutes : guestRoutes
  return (
    <div className="flex w-full flex-col">
      {routes.map((route) => (
        <SidebarItem key={route.href} icon={route.icon} label={route.label} href={route.href} />
      ))}
    </div>
  )
}
