'use client'

import { useAuth } from '@clerk/nextjs'

export default function DebugPage() {
  const { userId } = useAuth()

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Debug Information</h1>
      <div className="bg-gray-100 p-4 rounded">
        <p className="font-semibold">Your Clerk User ID:</p>
        <p className="font-mono text-sm bg-white p-2 rounded mt-2">
          {userId || 'Not logged in'}
        </p>
        <p className="text-sm text-gray-600 mt-2">
          Copy this ID and set it as NEXT_PUBLIC_TEACHER_ID in your .env file to become a teacher.
        </p>
      </div>
    </div>
  )
}
