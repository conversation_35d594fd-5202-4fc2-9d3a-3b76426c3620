'use client'

import { Category } from '@prisma/client'
import {
  FcEngineering,
  FcFilmReel,
  FcMultipleDevices,
  FcMusic,
  FcOldTimeCamera,
  FcSalesPerformance,
  FcSportsMode,
  FcReading,
  FcCalculator,
  FcBusinessman,
  FcPicture,
  FcLike,
} from 'react-icons/fc'
import { IconType } from 'react-icons'
import { CategoryItem } from './category-item'

interface CategoriesProps {
  items: Category[]
}

const iconMap: Record<Category['name'], IconType> = {
  'Ilmu Komputer': FcMultipleDevices,
  'Musik': FcMusic,
  'Kebugaran': FcSportsMode,
  'Fotografi': FcOldTimeCamera,
  'Akuntansi': FcSalesPerformance,
  'Teknik': FcEngineering,
  'Pembuatan Film': FcFilmReel,
  'Bahasa': FcReading,
  'Matematika': FcCalculator,
  'Bisnis': <PERSON>c<PERSON>us<PERSON><PERSON>,
  'Seni': FcPicture,
  'Memasak': <PERSON>cLike,
}

export const Categories = ({ items }: CategoriesProps) => {
  return (
    <div className="flex items-center gap-x-2 overflow-x-auto pb-2">
      {items.map((item) => (
        <CategoryItem key={item.id} label={item.name} icon={iconMap[item.name]} value={item.id} />
      ))}
    </div>
  )
}
