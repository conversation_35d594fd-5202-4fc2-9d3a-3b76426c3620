'use client'

import * as z from 'zod'
import axios from 'axios'
import { Pencil, PlusCircle, ImageIcon } from 'lucide-react'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { useRouter } from 'next/navigation'
import { Course } from '@prisma/client'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { FileUpload } from '@/components/file-upload'

interface ImageFormProps {
  initialData: Course
  courseId: string
}

const formSchema = z.object({
  imageUrl: z.string().min(1, {
    message: 'Image is required',
  }),
})

export const ImageForm = ({ initialData, courseId }: ImageFormProps) => {
  const [isEditing, setIsEditing] = useState(false)

  const toggleEdit = () => setIsEditing((current) => !current)

  const router = useRouter()

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await formSchema.parseAsync(values)
      await axios.patch(`/api/courses/${courseId}`, values)
      toast.success('Kursus diperbarui')
      toggleEdit()
      router.refresh()
    } catch {
      toast.error('Terjadi kesalahan')
    }
  }

  return (
    <div className="mt-6 rounded-md border bg-slate-100 p-4">
      <div className="flex items-center justify-between font-medium">
        Gambar Kursus
        <Button onClick={toggleEdit} variant="ghost">
          {isEditing && <>Batal</>}
          {!isEditing && (!initialData.imageUrl || initialData.imageUrl.trim() === '') && (
            <>
              <PlusCircle className="mr-2 h-4 w-4" />
              Tambah gambar
            </>
          )}
          {!isEditing && initialData.imageUrl && initialData.imageUrl.trim() !== '' && (
            <>
              <Pencil className="mr-2 h-4 w-4" />
              Edit gambar
            </>
          )}
        </Button>
      </div>
      {!isEditing &&
        (!initialData.imageUrl || initialData.imageUrl.trim() === '' ? (
          <div className="flex h-60 items-center justify-center rounded-md bg-slate-200">
            <ImageIcon className="h-10 w-10 text-slate-500" />
          </div>
        ) : (
          <div className="relative mt-2 aspect-video">
            <Image
              alt="Course Image"
              fill
              className="rounded-md object-cover"
              src={initialData.imageUrl}
              onError={(e) => {
                console.error('Image failed to load:', initialData.imageUrl);
                // You could set a fallback image here
              }}
            />
          </div>
        ))}
      {isEditing && (
        <div>
          <FileUpload
            endpoint="courseImage"
            onChange={(url) => {
              if (url) {
                onSubmit({ imageUrl: url })
              }
            }}
          />
          <div className="mt-4 text-xs text-muted-foreground">Rasio aspek 16:9 direkomendasikan</div>
        </div>
      )}
    </div>
  )
}
