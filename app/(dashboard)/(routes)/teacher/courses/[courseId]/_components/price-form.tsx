'use client'

import * as z from 'zod'
import axios from 'axios'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { Pencil } from 'lucide-react'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { useRouter } from 'next/navigation'
import { Course } from '@prisma/client'
import { Form, FormControl, FormField, FormItem, FormMessage, FormLabel } from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { formatPrice } from '@/lib/format'

interface PriceFormProps {
  initialData: Course
  courseId: string
}

const formSchema = z.object({
  price: z.coerce.number().optional().nullable(),
  isFree: z.boolean().default(false),
}).refine((data) => {
  // Either the course is free OR it has a price
  return data.isFree || (data.price !== null && data.price !== undefined && data.price > 0)
}, {
  message: "Kursus harus gratis atau memiliki harga",
  path: ["price"]
})

export const PriceForm = ({ initialData, courseId }: PriceFormProps) => {
  const [isEditing, setIsEditing] = useState(false)

  const toggleEdit = () => setIsEditing((current) => !current)

  const router = useRouter()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      price: initialData?.price || undefined,
      isFree: initialData?.isFree || false,
    },
  })

  const { isSubmitting, isValid } = form.formState

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const submitData = {
        isFree: values.isFree,
        price: values.isFree ? null : values.price
      }
      await axios.patch(`/api/courses/${courseId}`, submitData)
      toast.success('Kursus diperbarui')
      toggleEdit()
      router.refresh()
    } catch (error) {
      console.error('Error updating course:', error)
      toast.error('Terjadi kesalahan')
    }
  }

  return (
    <div className="mt-6 rounded-md border bg-slate-100 p-4">
      <div className="flex items-center justify-between font-medium">
        Harga Kursus
        <Button onClick={toggleEdit} variant="ghost">
          {isEditing ? (
            <>Batal</>
          ) : (
            <>
              <Pencil className="mr-2 h-4 w-4" />
              Edit Harga
            </>
          )}
        </Button>
      </div>
      {!isEditing && (
        <p className={cn('mt-2 text-sm', (!initialData.price && !initialData.isFree) && 'italic text-slate-500')}>
          {initialData.isFree
            ? <span className="text-green-600 font-semibold">GRATIS</span>
            : initialData.price
            ? formatPrice(initialData.price)
            : 'Tidak ada harga'}
        </p>
      )}
      {isEditing && (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="mt-4 space-y-4">
            <FormField
              control={form.control}
              name="isFree"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Kursus gratis
                    </FormLabel>
                    <p className="text-sm text-muted-foreground">
                      Centang jika kursus ini gratis untuk semua siswa
                    </p>
                  </div>
                </FormItem>
              )}
            />

            {!form.watch('isFree') && (
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        disabled={isSubmitting}
                        placeholder="Tetapkan harga untuk kursus Anda"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex items-center gap-x-2">
              <Button disabled={!isValid || isSubmitting} type="submit">
                Simpan
              </Button>
            </div>
          </form>
        </Form>
      )}
    </div>
  )
}
