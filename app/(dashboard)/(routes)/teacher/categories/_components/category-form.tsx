'use client'

import * as z from 'zod'
import axios from 'axios'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { useRouter } from 'next/navigation'
import { Plus } from 'lucide-react'

import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const formSchema = z.object({
  name: z.string().min(1, '<PERSON>a kategori diperlukan').max(50, 'Nama kategori terlalu panjang'),
})

export const CategoryForm = () => {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
    },
  })

  const { isSubmitting, isValid } = form.formState

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true)
      await axios.post('/api/categories', values)
      toast.success('Kategori berhasil dibuat')
      form.reset()
      router.refresh()
    } catch (error: any) {
      if (error.response?.status === 409) {
        toast.error('Kategori dengan nama ini sudah ada')
      } else {
        toast.error('Terjadi kesalahan')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          Tambah Kategori Baru
        </CardTitle>
        <CardDescription>
          Buat kategori baru untuk mengorganisir kursus-kursus Anda
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      disabled={isSubmitting || isLoading}
                      placeholder="Masukkan nama kategori (mis. Pemrograman Web)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button 
              disabled={!isValid || isSubmitting || isLoading} 
              type="submit"
              className="w-full sm:w-auto"
            >
              {isLoading ? 'Membuat...' : 'Buat Kategori'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
