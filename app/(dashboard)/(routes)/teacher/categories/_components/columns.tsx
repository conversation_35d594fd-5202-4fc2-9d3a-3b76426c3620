'use client'

import { ColumnDef } from '@tanstack/react-table'
import { ArrowUpDown, MoreHorizontal, Pencil, Trash } from 'lucide-react'
import { Category } from '@prisma/client'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { CategoryActions } from './category-actions'

type CategoryWithCourseCount = Category & {
  courses: { id: string }[]
}

export const columns: ColumnDef<CategoryWithCourseCount>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Nama <PERSON>i
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('name')}</div>
    },
  },
  {
    id: 'courseCount',
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Jumlah Kursus
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const courseCount = row.original.courses.length
      return (
        <Badge variant={courseCount > 0 ? 'default' : 'secondary'}>
          {courseCount} kursus
        </Badge>
      )
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const category = row.original
      return <CategoryActions category={category} />
    },
  },
]
