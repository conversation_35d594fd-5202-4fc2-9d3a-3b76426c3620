import { auth } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { isTeacher } from '@/lib/teacher'

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    const { userId } = await auth()
    const { name } = await req.json()
    const resolvedParams = await params

    if (!userId || !isTeacher(userId)) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    if (!name) {
      return new NextResponse('Name is required', { status: 400 })
    }

    // Check if category exists
    const existingCategory = await db.category.findUnique({
      where: { id: resolvedParams.categoryId }
    })

    if (!existingCategory) {
      return new NextResponse('Category not found', { status: 404 })
    }

    // Check if another category with the same name exists
    const duplicateCategory = await db.category.findFirst({
      where: { 
        name: { equals: name, mode: 'insensitive' },
        id: { not: resolvedParams.categoryId }
      }
    })

    if (duplicateCategory) {
      return new NextResponse('Category already exists', { status: 409 })
    }

    const category = await db.category.update({
      where: { id: resolvedParams.categoryId },
      data: { name },
    })

    return NextResponse.json(category)
  } catch (error) {
    console.error('[CATEGORY_PATCH]', error)
    return new NextResponse('Internal Error', { status: 500 })
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    const { userId } = await auth()
    const resolvedParams = await params

    if (!userId || !isTeacher(userId)) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Check if category exists
    const existingCategory = await db.category.findUnique({
      where: { id: resolvedParams.categoryId },
      include: { courses: true }
    })

    if (!existingCategory) {
      return new NextResponse('Category not found', { status: 404 })
    }

    // Check if category has courses
    if (existingCategory.courses.length > 0) {
      return new NextResponse('Cannot delete category with courses', { status: 400 })
    }

    await db.category.delete({
      where: { id: resolvedParams.categoryId },
    })

    return new NextResponse('Category deleted', { status: 200 })
  } catch (error) {
    console.error('[CATEGORY_DELETE]', error)
    return new NextResponse('Internal Error', { status: 500 })
  }
}
