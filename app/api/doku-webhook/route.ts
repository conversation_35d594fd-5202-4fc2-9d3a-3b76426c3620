import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { db } from '@/lib/db'
import { doku } from '@/lib/doku'

export async function POST(req: NextRequest) {
  try {
    const body = await req.text()
    const headersList = await headers()
    
    // Verify the webhook signature (implement based on <PERSON><PERSON>'s documentation)
    const signature = headersList.get('X-Doku-Signature')
    
    if (!signature) {
      return new NextResponse('Missing signature', { status: 400 })
    }

    // Parse the webhook payload
    const payload = JSON.parse(body)
    
    // Extract order information
    const orderId = payload.original_partner_reference_no
    const transactionStatus = payload.transaction_status_desc
    const paidAmount = payload.paid_amount
    
    if (!orderId) {
      return new NextResponse('Missing order ID', { status: 400 })
    }

    // Find the order in our database
    const dokuOrder = await db.dokuOrder.findUnique({
      where: { orderId },
      include: { course: true }
    })

    if (!dokuOrder) {
      return new NextResponse('Order not found', { status: 404 })
    }

    // Check if payment was successful
    if (transactionStatus === 'SUCCESS' && paidAmount >= dokuOrder.amount) {
      // Update order status
      await db.dokuOrder.update({
        where: { orderId },
        data: { status: 'completed' }
      })

      // Create purchase record
      await db.purchase.create({
        data: {
          userId: dokuOrder.userId,
          courseId: dokuOrder.courseId
        }
      })

      console.log(`Payment successful for order ${orderId}`)
    } else if (transactionStatus === 'FAILED') {
      // Update order status to failed
      await db.dokuOrder.update({
        where: { orderId },
        data: { status: 'failed' }
      })

      console.log(`Payment failed for order ${orderId}`)
    }

    return new NextResponse('OK', { status: 200 })
  } catch (error) {
    console.error('Doku webhook error:', error)
    return new NextResponse('Webhook error', { status: 500 })
  }
}
