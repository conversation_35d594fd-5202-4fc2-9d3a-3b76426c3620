import { currentUser } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { createDokuPayment } from '@/lib/doku'

export async function POST(req: NextRequest, { params }: { params: Promise<{ courseId: string }> }) {
  try {
    const resolvedParams = await params
    const user = await currentUser()
    if (!user || !user.id || !user.emailAddresses?.[0]?.emailAddress) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const course = await db.course.findUnique({ 
      where: { id: resolvedParams.courseId, isPublished: true } 
    })
    
    if (!course) {
      return new NextResponse('Course not found!', { status: 404 })
    }

    const purchase = await db.purchase.findUnique({
      where: { userId_courseId: { userId: user.id, courseId: resolvedParams.courseId } },
    })

    if (purchase) {
      return new NextResponse('Already purchased', { status: 400 })
    }

    // Convert USD to IDR (approximate rate: 1 USD = 15,000 IDR)
    const priceInIDR = Math.round((course.price || 0) * 15000)

    const orderId = `course_${resolvedParams.courseId}_${user.id}_${Date.now()}`

    const dokuPayment = await createDokuPayment({
      orderId,
      amount: priceInIDR,
      customerName: user.firstName + ' ' + user.lastName || 'Student',
      customerEmail: user.emailAddresses[0].emailAddress,
      description: `Course: ${course.title}`,
      successUrl: `${process.env.NEXT_PUBLIC_APP_URL}/courses/${course.id}?success=1`,
      cancelUrl: `${process.env.NEXT_PUBLIC_APP_URL}/courses/${course.id}?cancelled=1`,
    })

    // Store the order in database for tracking
    await db.dokuOrder.create({
      data: {
        orderId,
        userId: user.id,
        courseId: resolvedParams.courseId,
        amount: priceInIDR,
        status: 'pending',
      },
    })

    return NextResponse.json({ 
      virtualAccountNumber: dokuPayment.paymentUrl,
      orderId: dokuPayment.orderId,
      amount: priceInIDR,
      currency: 'IDR'
    })
  } catch (error) {
    console.error('Doku checkout error:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
}
